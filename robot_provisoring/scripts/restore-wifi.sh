#!/bin/bash
# restore-wifi.sh - Przywracanie normalnej konfiguracji WiFi (klient)

set -e

echo "=== Przywracanie normalnej konfiguracji WiFi ==="

# Sprawdź czy NetworkManager jest aktywny
if ! systemctl is-active --quiet NetworkManager; then
    echo "Włączanie NetworkManager..."
    systemctl enable NetworkManager
    systemctl start NetworkManager
    sleep 3
fi

# Usuń Access Point ARGUS-AP
echo "Usuwanie Access Point ARGUS-AP..."
if nmcli con show | grep -q "ARGUS-AP"; then
    nmcli con delete "ARGUS-AP"
    echo "✓ Access Point ARGUS-AP został usunięty"
else
    echo "ℹ Access Point ARGUS-AP nie istnieje"
fi

# Usuń inne połączenia AP na wlan0 (jeśli istnieją)
echo "Usuwanie innych połączeń Access Point..."
nmcli con show | grep -E "(wifi|802-11-wireless)" | grep "wlan0" | awk '{print $1}' | while read con; do
    if nmcli con show "$con" | grep -q "802-11-wireless.mode.*ap"; then
        echo "Usuwanie AP: $con"
        nmcli con delete "$con" 2>/dev/null || true
    fi
done

# Wyłącz i włącz radio WiFi
echo "Resetowanie interfejsu WiFi..."
nmcli radio wifi off
sleep 2
nmcli radio wifi on
sleep 3

# Usuń reguły iptables NAT (jeśli istnieją)
echo "Usuwanie reguł NAT..."
iptables -t nat -D POSTROUTING -o eth0 -j MASQUERADE 2>/dev/null || true
iptables -D FORWARD -i eth0 -o wlan0 -m state --state RELATED,ESTABLISHED -j ACCEPT 2>/dev/null || true
iptables -D FORWARD -i wlan0 -o eth0 -j ACCEPT 2>/dev/null || true

# Zapisz zmiany iptables
netfilter-persistent save 2>/dev/null || true

# Usuń konfigurację IP forwarding (opcjonalnie)
echo "Wyłączanie IP forwarding..."
if [ -f /etc/sysctl.d/routed-ap.conf ]; then
    rm /etc/sysctl.d/routed-ap.conf
    echo "✓ Usunięto konfigurację IP forwarding"
fi

# Zastosuj zmiany sysctl
sysctl net.ipv4.ip_forward=0 2>/dev/null || true

echo "=== Konfiguracja przywrócona ==="
echo ""
echo "Robot jest teraz gotowy do połączenia z normalną siecią WiFi."
echo ""
echo "Aby połączyć się z siecią WiFi użyj:"
echo "  nmcli dev wifi list"
echo "  nmcli dev wifi connect 'NAZWA_SIECI' password 'HASŁO'"
echo ""
echo "Lub użyj interaktywnego narzędzia:"
echo "  nmtui"
echo ""
echo "Status interfejsów:"
nmcli dev status
