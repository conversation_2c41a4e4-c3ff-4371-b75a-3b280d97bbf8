#!/bin/bash
# 04-ros2.sh - Instalacja ROS2 Jazzy ze zrodel na Debian 12 Bookworm

set -e

echo "=== Instalacja ROS2 Jazzy ze zrodel ==="

apt update
apt install -y software-properties-common


# Konfiguracja repozytoriow ROS2 za pomoca ros-apt-source
echo "Konfiguracja repozytoriow ROS2..."
apt update && apt install -y curl

# Sprawdzenie wersji systemu
. /etc/os-release
echo "Wykryto system: $PRETTY_NAME"
echo "Codename: $VERSION_CODENAME"

# Pobranie najnowszej wersji ros-apt-source
export ROS_APT_SOURCE_VERSION=$(curl -s https://api.github.com/repos/ros-infrastructure/ros-apt-source/releases/latest | grep -F "tag_name" | awk -F\" '{print $4}')
echo "Wersja ros-apt-source: $ROS_APT_SOURCE_VERSION"

# Pobranie i instalacja ros-apt-source
echo "Pobieranie ros-apt-source dla $VERSION_CODENAME..."
curl -L -o /tmp/ros2-apt-source.deb "https://github.com/ros-infrastructure/ros-apt-source/releases/download/${ROS_APT_SOURCE_VERSION}/ros2-apt-source_${ROS_APT_SOURCE_VERSION}.${VERSION_CODENAME}_all.deb"

if [ -f /tmp/ros2-apt-source.deb ]; then
    echo "Instalacja ros-apt-source..."
    dpkg -i /tmp/ros2-apt-source.deb
    rm -f /tmp/ros2-apt-source.deb
    echo "✓ ros-apt-source zainstalowany"
else
    echo "✗ Nie udało się pobrac ros-apt-source dla $VERSION_CODENAME"
    echo "Sprawdz czy ros-apt-source obsługuje Debian $VERSION_CODENAME"
    exit 1
fi

# Instalacja narzędzi deweloperskich ROS2
echo "Instalacja narzędzi deweloperskich ROS2..."
apt update

# Instalacja podstawowych narzędzi testowych
apt install -y \
    python3-flake8-blind-except \
    python3-flake8-class-newline \
    python3-flake8-deprecated \
    python3-mypy \
    python3-pip \
    python3-pytest \
    python3-pytest-cov \
    python3-pytest-mock \
    python3-pytest-repeat \
    python3-pytest-rerunfailures \
    python3-pytest-runner \
    python3-pytest-timeout

# Instalacja ros-dev-tools lub jego składnikow
if apt-cache show ros-dev-tools >/dev/null 2>&1; then
    echo "Instalacja ros-dev-tools..."
    apt install -y ros-dev-tools
else
    echo "ros-dev-tools niedostępny - instalacja składnikow indywidualnie..."
    apt install -y \
        python3-colcon-common-extensions \
        python3-rosdep \
        python3-vcstool \
        wget
fi

echo "Instalacja dodatkowych zaleznosci ROS2..."
apt install -y \
    libasio-dev \
    libtinyxml2-dev \
    libcunit1-dev \
    libssl-dev \
    qtbase5-dev \
    libqt5core5a \
    libqt5gui5 \
    libqt5opengl5-dev \
    libqt5widgets5 \
    libxaw7-dev \
    libxrandr-dev \
    libeigen3-dev \
    libxml2-utils \
    python3-lark \
    python3-empy \
    python3-catkin-pkg \
    python3-distutils \
    python3-importlib-metadata \
    python3-argcomplete \
    python3-psutil \
    libacl1-dev \
    liblog4cxx-dev \
    libtinyxml-dev \
    libgtest-dev \
    libgmock-dev \
    libconsole-bridge-dev \
    libfastcdr-dev \
    libfastrtps-dev \
    liburdfdom-headers-dev \
    liburdfdom-dev \
    libkdl-parser-dev \
    liborocos-kdl-dev


cd /root/ros2_jazzy

# Pobieranie zrodel ROS2 Jazzy
echo "Pobieranie zrodel ROS2 Jazzy (moze potrwac kilka minut)..."

# Sprawdzenie czy zrodła juz istnieja
if [ -d "src" ] && [ "$(ls -A src)" ]; then
    echo "Wykryto istniejace zrodła ROS2 - aktualizacja..."
    echo "UWAGA: Jesli aktualizacja się nie powiedzie, zrodła pozostana nietknięte"

    # Aktualizacja istniejacych repozytoriow (nie krytyczne jesli się nie powiedzie)
    echo "Aktualizacja istniejacych repozytoriow..."
    vcs pull src || echo "Niektore repozytoria nie zostały zaktualizowane (to nie jest krytyczne)"

    # Pobranie nowych repozytoriow jesli zostały dodane
    echo "Sprawdzanie nowych repozytoriow..."
    vcs import --input https://raw.githubusercontent.com/ros2/ros2/jazzy/ros2.repos src --skip-existing || {
        echo "UWAGA: Nie udało się pobrac nowych repozytoriow"
        echo "Kontynuuję z istniejacymi zrodłami..."
    }
else
    echo "Pobieranie swiezych zrodeł ROS2..."
    # Pierwsze pobieranie - wszystkie repozytoria
    if ! vcs import --input https://raw.githubusercontent.com/ros2/ros2/jazzy/ros2.repos src; then
        echo "BŁaD: Nie udało się pobrac zrodeł ROS2"
        echo "Sprawdz połaczenie internetowe i sprobuj ponownie"
        exit 1
    fi
fi

echo "✓ zrodła ROS2 gotowe"

# Inicjalizacja rosdep
echo "Inicjalizacja rosdep..."
if [ ! -f /etc/ros/rosdep/sources.list.d/20-default.list ]; then
    rosdep init
else
    echo "rosdep już zainicjalizowany - pomijam"
fi

# Napraw uprawnienia i aktualizuj rosdep
rosdep fix-permissions 2>/dev/null || true
rosdep update

# Instalacja zaleznosci ROS2 za pomoca rosdep 
echo "Instalacja zaleznosci ROS2 za pomoca rosdep..."
rosdep install --from-paths src --ignore-src -y --skip-keys "fastcdr rti-connext-dds-6.0.1 urdfdom_headers"

# Sprawdzenie czy srodowisko jest czyste 
echo "Sprawdzanie srodowiska..."
if printenv | grep -i ROS > /dev/null; then
    echo "UWAGA: Wykryto zmienne srodowiskowe ROS. Upewnij się, ze nie masz sourced innych instalacji ROS."
    echo "Kontynuuję instalację..."
fi

# Budowanie ROS2 ze zrodel
echo "Budowanie ROS2 ze zrodel"
echo "To moze byc dlugi proces - badz cierpliwy!"

# Ustawienie zmiennych srodowiskowych dla budowania
export MAKEFLAGS="-j$(nproc)"
export COLCON_WS=/root/ros2_jazzy

# Przejscie do workspace
cd /root/ros2_jazzy/

# Budowanie z pominieciem problematycznych pakietow i testow
echo "Budowanie pakietow ROS2 ..."
colcon build --symlink-install \
    --executor sequential \
    --cmake-args -DFETCHCONTENT_FULLY_DISCONNECTED=ON \
    --cmake-args -DBUILD_TESTING=OFF \
    # --continue-on-error \
    # --packages-skip iceoryx_hoofs iceoryx_posh iceoryx_binding_c iceoryx_introspection test_interface_files test_osrf_testing_tools_cpp cyclonedx ament_cmake_google_benchmark

echo "✓ Budowanie ROS2 zakonczone!"

# Konfiguracja srodowiska ROS2 w .bashrc dla root
echo "Konfiguracja srodowiska ROS2..."
# Sprawdz czy konfiguracja ROS2 juz istnieje
if ! grep -q "ROS2 Jazzy Environment" /root/.bashrc; then
    cat >> /root/.bashrc << 'EOF'

# ROS2 Jazzy Environment (ze zrodel)
source /root/ros2_jazzy/install/setup.bash

# ROS2 Domain ID dla ARGUS
export ROS_DOMAIN_ID=42

# ROS2 Network communication enabled for robot-controller connection
export ROS_LOCALHOST_ONLY=0

# Dodanie workspace do PATH
export COLCON_WS=/root/ros2_jazzy

EOF
else
    echo "Konfiguracja ROS2 juz istnieje w .bashrc - pomijam"
fi

# Tworzenie skryptu aktywacji dla innych uzytkownikow
echo "Tworzenie skryptu aktywacji..."
cat > /opt/ros2_setup.sh << 'EOF'
#!/bin/bash
# Skrypt aktywacji ROS2 Jazzy (ze zrodel)
source /root/ros2_jazzy/install/setup.bash
export ROS_DOMAIN_ID=42
export ROS_LOCALHOST_ONLY=0
export COLCON_WS=/root/ros2_jazzy
EOF

chmod +x /opt/ros2_setup.sh

# Test instalacji 
echo "Test instalacji ROS2..."
cd /root/ros2_jazzy/
source install/setup.bash
export ROS_DOMAIN_ID=42
export ROS_LOCALHOST_ONLY=0


echo "=== Instalacja ROS2 Jazzy ze zrodel zakonczona ==="
echo ""
echo "Aby aktywowac ROS2 w nowej sesji, uzyj:"
echo "  source /opt/ros2_setup.sh"
echo ""
echo "Lub uruchom nowy terminal - ROS2 zostanie automatycznie aktywowany."
echo ""
echo "Aby przetestowac instalację, uruchom w dwoch terminalach:"
echo "  Terminal 1: ros2 run demo_nodes_cpp talker"
echo "  Terminal 2: ros2 run demo_nodes_py listener"