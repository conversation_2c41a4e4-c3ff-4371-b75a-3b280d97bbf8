#!/bin/bash
# 05-wan.sh - Konfiguracja sieci i Access Point z NetworkManager

set -e

echo "=== Konfiguracja sieci i Access Point z NetworkManager ==="

# Instalacja pakietow sieciowych
echo "Instalacja pakietow sieciowych..."
apt install -y \
    network-manager \
    iptables-persistent \
    bridge-utils

# Zatrzymanie starych uslug sieciowych
echo "Zatrzymywanie starych uslug sieciowych..."
systemctl stop hostapd 2>/dev/null || true
systemctl stop dnsmasq 2>/dev/null || true
systemctl stop dhcpcd 2>/dev/null || true
systemctl stop wpa_supplicant 2>/dev/null || true

# Wyłączenie starych usług
systemctl disable hostapd 2>/dev/null || true
systemctl disable dnsmasq 2>/dev/null || true
systemctl disable dhcpcd 2>/dev/null || true
systemctl disable wpa_supplicant 2>/dev/null || true

# Upewnienie się że NetworkManager jest włączony
echo "Włączanie NetworkManager..."
systemctl enable NetworkManager
systemctl start NetworkManager

# Poczekaj na uruchomienie NetworkManager
sleep 5

# Usuń istniejące połączenia WiFi na wlan0
echo "Usuwanie starych połączeń WiFi..."
nmcli con show | grep wlan0 | awk '{print $1}' | while read con; do
    nmcli con delete "$con" 2>/dev/null || true
done

# Wyłącz wlan0 jeśli jest aktywny
nmcli radio wifi off 2>/dev/null || true
sleep 2
nmcli radio wifi on 2>/dev/null || true
sleep 3

# Tworzenie Access Point przez NetworkManager
echo "Tworzenie Access Point ARGUS..."

# Stwórz połączenie Access Point
nmcli con add type wifi ifname wlan0 con-name "ARGUS-AP" autoconnect yes ssid "ARGUS"

# Konfiguracja Access Point
nmcli con modify "ARGUS-AP" 802-11-wireless.mode ap
nmcli con modify "ARGUS-AP" 802-11-wireless.band bg
nmcli con modify "ARGUS-AP" 802-11-wireless.channel 7
nmcli con modify "ARGUS-AP" ipv4.method shared
nmcli con modify "ARGUS-AP" ipv4.addresses ***********/24

# Konfiguracja zabezpieczeń WiFi
nmcli con modify "ARGUS-AP" wifi-sec.key-mgmt wpa-psk
nmcli con modify "ARGUS-AP" wifi-sec.psk "argusik123"

# Aktywuj połączenie Access Point
echo "Aktywacja Access Point..."
nmcli con up "ARGUS-AP"

# Konfiguracja przekierowania ruchu (NAT)
echo "Konfiguracja NAT..."
cat > /etc/sysctl.d/routed-ap.conf << 'EOF'
# Enable IPv4 routing
net.ipv4.ip_forward=1
EOF

# Zastosuj ustawienia sysctl
sysctl -p /etc/sysctl.d/routed-ap.conf

# Konfiguracja iptables dla NAT
echo "Konfiguracja iptables..."
# Wyczyść istniejące reguły NAT
iptables -t nat -F POSTROUTING 2>/dev/null || true
iptables -F FORWARD 2>/dev/null || true

# Dodaj nowe reguły NAT
iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
iptables -A FORWARD -i eth0 -o wlan0 -m state --state RELATED,ESTABLISHED -j ACCEPT
iptables -A FORWARD -i wlan0 -o eth0 -j ACCEPT

# Zapisanie regul iptables
netfilter-persistent save

# Kopiowanie pliku konfiguracyjnego sieci
PROVISION_DIR="$(dirname "$(dirname "$(realpath "$0")")")"
mkdir -p /etc/argusik
if [ -f "$PROVISION_DIR/network/network.conf" ]; then
    echo "Kopiowanie konfiguracji sieci..."
    cp "$PROVISION_DIR/network/network.conf" /etc/argusik/
fi

# Sprawdzenie statusu Access Point
echo "Sprawdzanie statusu Access Point..."
sleep 5

# Sprawdź czy połączenie jest aktywne
if nmcli con show --active | grep -q "ARGUS-AP"; then
    echo "✓ Access Point ARGUS-AP jest aktywny"
else
    echo "⚠ Próba ponownej aktywacji Access Point..."
    nmcli con up "ARGUS-AP" || echo "✗ Błąd aktywacji Access Point"
fi

# Sprawdź IP interfejsu wlan0
WLAN0_IP=$(ip addr show wlan0 | grep "inet " | awk '{print $2}' | head -n1)
if [ -n "$WLAN0_IP" ]; then
    echo "✓ Interfejs wlan0 ma IP: $WLAN0_IP"
else
    echo "⚠ Interfejs wlan0 nie ma przypisanego IP"
fi

# Pokaż status NetworkManager
echo "Status NetworkManager:"
nmcli dev status | grep wlan0 || echo "wlan0 nie znaleziony w NetworkManager"

echo "=== Konfiguracja sieci zakonczona ==="
echo "UWAGA: Robot jest teraz dostepny jako Access Point:"
echo "SSID: ARGUS"
echo "Haslo: argusik123"
echo "IP: ***********"
echo ""
echo "Sprawdź połączenie poleceniem: nmcli con show --active"